#contactpage{
    min-height: calc(100vh - 4rem);
    width: 100vw;
    max-width: 60rem;
    margin: 0 auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
      
}
.contactpagetitle{
    margin-bottom: 1rem;
    font-size: 3rem;
}
.clientdesc{
    margin: 0px 10px;
    font-size: 1rem;
    font-weight: 300;
}
.clientimgs{
    margin:  1rem 0 ;
    display: flex;
    flex-wrap: wrap;

}
.clientimg{
    object-fit: cover;
    width: 23%;
    min-width: 9rem;
    margin: auto;
    padding: 0.25rem;
    border: 1px solid rgb(175, 175, 175);

}

#contact{
    padding: 2rem;

}
.contactdesc{
    padding: 1rem;
    font-size: medium;
    font-weight: 300;
}
.contactform{
    margin: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90vw;
    max-width: 60rem;

}

.name,.email,.msg{
    font-size: medium;
    width: 100%;
    max-width:40rem ;
    margin: 0.5rem;
    padding: 0.5rem 1rem;
    color: rgb(43, 43, 43);
    border: none;
    border-radius: 0.5rem;
    background: rgb(232, 232, 232);

}

.submitbtn{
    background: rgb(1, 162, 206);
    color: white;
    border: none;
    padding: 0.75rem 3.5rem;
    border-radius: 0.5rem;
    margin: 2rem;


}
.submitbtn:hover{
    background-color:#05c4fd ;
}
.links{
    display: flex;
    flex-wrap: wrap;
}
.link{
    object-fit: cover;
    height: 3rem;
    width: 3rem;
    margin: 0 0.75rem;
    
}



.Hapclinets{


 


}

.cli {

    text-align:  center;
    color: rgb(255, 255, 255);
    font-size: 23px;
    font-weight: 600;



}

.happyclients {

    display: flex;
    text-align: center;
    /* border: 1px solid red; */
    /* margin-left: 0rem; */




}

.clientcard {
    cursor: pointer;
    width: 300px;
    height: 200px;
    margin: 2rem 10px;
    /* margin-left: 3rem; */
    color: #171718;
    /* background-color: rgb(255, 255, 255); */
    background: linear-gradient(135deg, rgb(255, 255, 255)0%, rgb(255, 255, 255)100%);
    box-shadow: 4px 5px 16px 13px rgba(67, 66, 66, 0.2);


}

.clientcard:hover{
    transform: scale(0.98);
    -webkit-transition: all 350ms ease;
    transition: all 350ms ease;
    

}
.logos {
    margin: 10px auto;
    width: 27px;
    height: 27px;
}

.count {
    margin-top: -12px;
    font-size: 20px;
    color: #1a1a1a;

}

.counters {
    font-size: 28px;
    font-weight: 500;
}

.hapyy {
    color: #616161;
    font-size: 15px;

}



.allpart {
    margin: 4rem auto;
    justify-content: left;
    margin-left: 0rem;
}

.mypats {
    text-align: left;
    color: rgb(79, 79, 79);
    font-size: 23px;
    font-weight: 600;

}

.parters {
    margin-top: -2rem;
    display: flex;
    justify-content: left;
}

.compa {
    width: 70px;
    height: 70px;
    margin: 4rem 1rem;
}



.myball {
    cursor: pointer;
    margin: 2rem 8rem;

    width: 50px;
    height: 50px;
    background-size: cover;
    background-position: center center;
    box-shadow: 0 5px 5px 5px rgba(159, 159, 159, 0.1);
    background-color: rgb(255, 255, 255);
    animation: animate 5s ease-in-out infinite;
    transition: all .2s ease-in-out;
}

.myball:hover {
    color: #fefefe;
    background-color: #000000;
}

.clientcard:hover,
.myball:hover {
    transform: scale(0.98);
    -webkit-transition: all 350ms ease;
    transition: all 350ms ease;



}




@keyframes animate {

    0%,
    100% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }

    50% {
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
}


@media screen and (max-width: 720px) {


    .Hapclinets {

        justify-content: center;

    }

    .cli {

        text-align: center;
        color: rgb(79, 79, 79);
        font-size: 23px;
        font-weight: 600;



    }

    .happyclients {

        display: grid;
        justify-content: center;


    }

    .clientcard {
        height: 200px;
        margin: 5px 0px;
    

    }


    

}