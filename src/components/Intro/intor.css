#intro{
    height: calc(100vh - 4rem);
    width: 100vw;
    max-width: 75rem;
    margin: 0 auto;
    overflow: hidden;
    position: relative;

}

.bg {

     position: absolute;
     top: 1rem;
     right: 0;
     z-index: -1;
     object-fit: cover;
     height: 98vh;
     width: auto;
     /* border-radius: 1rem; */
 
   


}
.introContent{
    height: 100vh;
    width: 100vw;
    padding: 2rem;
    font-size: 3rem;
    font-weight: 700;
    display: flex;
    flex-direction: column;
    justify-content: center;



}

.hello{
    font-size: 1.75rem;
    font-weight: 100;
}
.introName{
    color: rgb(25, 174, 254);
}
.type{
    font-weight: 100;
    font-size: 20px;
}
.intropara{
    font-size: medium;
    font-weight: 200;
    letter-spacing: 1px;
    /* color: white */

}
.btndinv{
    
}
.btn{
    margin-top: 10px;
    background: rgba(216, 220, 220, 0.829);
    border: none; 
    border-radius: 2rem;
    cursor: pointer;
    padding: 3px 16px; 
    font-size: 16px; 
    font-weight: 400;
    border-radius: 3rem;  /* Rounded corners */
    cursor: pointer;
    object-fit: cover;



}
.btnImg{
    text-decoration: none;
    color: rgb(0, 0, 0);
    object-fit: cover;
    height: 1rem;

}

.btnImg:hover{
    color: aliceblue;
}
  
.btn:hover{
    color: white;
    background-color: rgb(23, 158, 254);
}



@media screen and (max-width:840px) {
    .bg{
        right: -10vw;
    }

    .introContent{
        font-size: 6vw;
    }
    .hello{
        font-size: 4.5vw;
    }
  

    .btn {
        font-size: 12px;  /* Further reduce font size */
        padding: 6px 12px; /* Adjust padding for small screens */
      }
    
      .btnImg {
        width: 14px;    /* Further reduce image size */
        height: 14px;
      }
    
    
}

@media screen and (max-width:700px) {

    .introContent{
        margin: 1rem;
        
    }
    .bg{
        right: -15vw;
    }
    .intropara{

        color: rgb(255, 255, 255);
        padding: 10px 10px;
        background-color: rgba(6, 6, 6, 0.2);
    
    }
   

    .btn {
        
        background: rgba(32, 192, 255, 0.829);
        font-size: 12px;  /* Further reduce font size */
        padding: 6px 12px; /* Adjust padding for small screens */
 

      }
    
      .btnImg {
        width: 14px;    /* Further reduce image size */
        height: 14px;
      }
       .type{
       color: white;
     }


      
    
  
    
}


