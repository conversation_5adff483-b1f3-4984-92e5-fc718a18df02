{"name": "protfolio-hassan", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^3.11.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "react-scroll-trigger": "^0.6.14", "react-simple-typewriter": "^5.0.1", "reactstrap": "^9.2.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}