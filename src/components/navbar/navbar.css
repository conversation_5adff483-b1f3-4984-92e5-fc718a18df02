.navbar{
    background: rgb(25, 117, 223);
    /* background: linear-gradient(135deg, rgb(255, 175, 15)40%, rgb(62, 183, 248)100%); */

    height: 3rem;
    width: 100vw;
    max-width: 90rem;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 3;


}

.logo{
    
    padding-top: -2rem;
    object-fit: cover;
    height: auto;
    width: 30px;
    /* background-color: rgb(14, 14, 14); */
    
 
}

.destopMenuListItem{
    margin:1rem ;
    cursor: pointer;
    color: rgb(247, 246, 246);
    font-weight: 300;



}

.destopMenuListItem:hover{
    color: rgb(9, 9, 9);
    padding-bottom: 0.2rem;
    border-bottom: 2px solid rgb(255, 255, 255);
}

.desktopMenubtn{
   background: rgb(244, 242, 242);
   height: 30px;
   width: 60%;
   color: black;
   border: none;
   display: flex;
   align-items: center;
   justify-content: center;
   padding: 0 1rem;
   border-radius: 2rem;

}
.desktopMenubtn:hover{
    color: white;
    /* background-color: rgb(217, 25, 25); */
    background: rgb(25, 117, 223);

}
.desktopMenuImg {

    object-fit: cover;
    height: 1rem;
    width: 1rem;
    margin: 1rem;
}


.active{
    color: rgb(0, 0, 0);
    padding-bottom: 0.2rem;
    border-bottom: 2px solid rgb(255, 255, 255);

}
 



.navbar2{
    position: sticky;


}
/* Menu icon */
.modMenu {
    display:none;
    justify-content: center;
    position: fixed;
    top: 0;
    margin: 1rem;
    z-index: 1; 
    cursor: pointer;
    height: 2rem;
    width: 2rem;
}
.modMenus{
    display: flex;
    justify-content: center;
    position: fixed;
    top: 0;
    margin: 1rem;
    z-index: 1; 
    cursor: pointer;
    height: 2rem;
    width: 2rem;


}

/* Navigation Menu */
.navMenu {
    position: fixed;
    top: 0;
    left: -100%; /* Hide the menu off-screen to the left initially */
    width: 150px;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 30px;
    transition: left 0.5s ease; /* Smooth transition for sliding */
    z-index: 1000; /* To place it below the menu icon */
}

/* When the menu is visible */
.navMenu.show {
    left: 0; /* Bring the menu into view from the left */
}

/* List items in the menu */
.listItem {
    color: rgb(60, 60, 60);
    padding: 10px 10px;
    font-size: 15px;
    font-weight: 300;
    cursor: pointer;
}

.listItem:hover {
    color: #00bfff;
}


@media screen and (max-width:720px) {
    .modMenu{
        display: flex;
    }
    .desktopMenu{
        display: none;
    }
    .desktopMenubtn{
        display: none;
    }
    
}  
